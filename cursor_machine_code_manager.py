import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import os
import json
import shutil
import subprocess
import stat
import time
import ctypes
import sys
import uuid
import winreg
from datetime import datetime

class CursorMachineCodeManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Cursor机器码管理器 - 管理员版")
        self.root.geometry("1000x800")
        self.root.resizable(True, True)

        # Cursor配置目录路径
        self.cursor_appdata_path = os.path.join(os.environ['APPDATA'], 'Cursor')

        # 检查管理员权限（此时应该已经是管理员了）
        self.is_admin = self.check_admin_privileges()

        # 创建界面
        self.create_widgets()

        # 初始化显示当前机器码
        self.refresh_machine_codes()

        # 显示权限状态
        self.update_admin_status()

        # 获取系统MachineGuid
        self.system_machine_guid = self.get_system_machine_guid()
    
    def get_cursor_machine_codes(self):
        """获取Cursor的所有机器码"""
        machine_codes = {
            'main_machine_id': 'N/A',
            'telemetry_machine_id': 'N/A',
            'telemetry_mac_machine_id': 'N/A',
            'telemetry_dev_device_id': 'N/A',
            'telemetry_sqm_id': 'N/A',
            'cursor_installed': False
        }
        
        try:
            # 检查Cursor是否安装
            if not os.path.exists(self.cursor_appdata_path):
                return machine_codes
            
            machine_codes['cursor_installed'] = True
            
            # 读取主机器ID文件
            machineid_file = os.path.join(self.cursor_appdata_path, 'machineid')
            if os.path.exists(machineid_file):
                with open(machineid_file, 'r', encoding='utf-8') as f:
                    machine_codes['main_machine_id'] = f.read().strip()
            
            # 读取storage.json中的遥测ID
            storage_file = os.path.join(self.cursor_appdata_path, 'User', 'globalStorage', 'storage.json')
            if os.path.exists(storage_file):
                with open(storage_file, 'r', encoding='utf-8') as f:
                    storage_data = json.load(f)
                    
                    machine_codes['telemetry_machine_id'] = storage_data.get('telemetry.machineId', 'N/A')
                    machine_codes['telemetry_mac_machine_id'] = storage_data.get('telemetry.macMachineId', 'N/A')
                    machine_codes['telemetry_dev_device_id'] = storage_data.get('telemetry.devDeviceId', 'N/A')
                    machine_codes['telemetry_sqm_id'] = storage_data.get('telemetry.sqmId', 'N/A')
            
        except Exception as e:
            messagebox.showerror("错误", f"获取机器码失败: {str(e)}")
        
        return machine_codes

    def get_system_machine_guid(self):
        """获取系统MachineGuid"""
        try:
            # 从注册表读取系统MachineGuid
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SOFTWARE\Microsoft\Cryptography") as key:
                machine_guid, _ = winreg.QueryValueEx(key, "MachineGuid")
                return machine_guid
        except Exception as e:
            self.log_status(f"[失败] 获取系统MachineGuid失败: {str(e)}")
            return "N/A"

    def reset_system_machine_guid(self):
        """重置系统MachineGuid"""
        if not self.is_admin:
            messagebox.showerror("权限错误", "重置系统MachineGuid需要管理员权限！")
            return False

        try:
            # 生成新的GUID
            new_guid = str(uuid.uuid4())

            # 写入注册表
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SOFTWARE\Microsoft\Cryptography",
                               0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)

            self.log_status(f"[成功] 系统MachineGuid已重置为: {new_guid}")
            self.system_machine_guid = new_guid
            return True

        except Exception as e:
            error_msg = f"重置系统MachineGuid失败: {str(e)}"
            self.log_status(f"[失败] {error_msg}")
            messagebox.showerror("重置失败", error_msg)
            return False

    def reset_all_telemetry_ids(self):
        """重置所有遥测相关ID（Mac机器ID、设备ID、SQMID）"""
        try:
            success_count = 0
            total_locations = 0

            # 1. 重置storage.json中的ID
            self.log_status("[处理] 重置storage.json中的遥测ID...")
            storage_file = os.path.join(self.cursor_appdata_path, 'User', 'globalStorage', 'storage.json')
            storage_dir = os.path.dirname(storage_file)

            # 如果目录不存在，创建目录
            if not os.path.exists(storage_dir):
                os.makedirs(storage_dir, exist_ok=True)
                self.log_status("[信息] 创建globalStorage目录")

            # 生成新的ID
            new_mac_machine_id = str(uuid.uuid4())
            new_device_id = str(uuid.uuid4())
            new_sqm_id = str(uuid.uuid4())

            # 读取现有配置或创建新配置
            if os.path.exists(storage_file):
                with open(storage_file, 'r', encoding='utf-8') as f:
                    storage_data = json.load(f)
                self.log_status("[信息] 读取现有storage.json文件")
            else:
                storage_data = {}
                self.log_status("[信息] 创建新的storage.json文件")

            # 更新所有遥测ID
            storage_data['telemetry.macMachineId'] = new_mac_machine_id
            storage_data['telemetry.devDeviceId'] = new_device_id
            storage_data['telemetry.sqmId'] = new_sqm_id

            # 写回文件
            with open(storage_file, 'w', encoding='utf-8') as f:
                json.dump(storage_data, f, indent=2, ensure_ascii=False)

            success_count += 1
            total_locations += 1
            self.log_status("[成功] storage.json中的ID已重置")

            # 2. 重置系统底层硬件标识符
            self.log_status("[处理] 重置系统底层硬件标识符...")
            hardware_success = self.reset_system_hardware_ids()
            if hardware_success:
                success_count += 1
            total_locations += 1

            # 3. 重置注册表中的遥测ID
            self.log_status("[处理] 重置注册表中的遥测ID...")
            registry_success = self.reset_registry_telemetry_ids(new_mac_machine_id, new_device_id, new_sqm_id)
            if registry_success:
                success_count += 1
            total_locations += 1

            # 4. 重置其他可能的配置文件
            self.log_status("[处理] 重置其他配置文件中的遥测ID...")
            config_success = self.reset_config_files_telemetry_ids(new_mac_machine_id, new_device_id, new_sqm_id)
            if config_success:
                success_count += 1
            total_locations += 1

            self.log_status(f"[成功] Mac机器ID已重置为: {new_mac_machine_id}")
            self.log_status(f"[成功] 设备ID已重置为: {new_device_id}")
            self.log_status(f"[成功] SQMID已重置为: {new_sqm_id}")
            self.log_status(f"[完成] 遥测ID重置完成: {success_count}/{total_locations} 个位置成功")

            return success_count > 0

        except Exception as e:
            error_msg = f"重置遥测ID失败: {str(e)}"
            self.log_status(f"[失败] {error_msg}")
            messagebox.showerror("重置失败", error_msg)
            return False

    def reset_system_hardware_ids(self):
        """重置系统底层硬件标识符"""
        try:
            success_count = 0

            # 1. 重置网卡MAC地址相关的注册表项
            self.log_status("[处理] 重置网卡MAC地址标识符...")
            try:
                # 查找网络适配器注册表项
                network_key = r"SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, network_key, 0, winreg.KEY_ENUMERATE_SUB_KEYS) as key:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            if subkey_name.isdigit():  # 网卡子键通常是数字
                                adapter_key = f"{network_key}\\{subkey_name}"
                                try:
                                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, adapter_key, 0, winreg.KEY_SET_VALUE) as adapter:
                                        # 生成新的MAC地址
                                        new_mac = "02" + "".join([f"{uuid.uuid4().hex[i:i+2]}" for i in range(0, 10, 2)])
                                        winreg.SetValueEx(adapter, "NetworkAddress", 0, winreg.REG_SZ, new_mac)
                                        success_count += 1
                                        self.log_status(f"[成功] 网卡 {subkey_name} MAC地址已重置")
                                except:
                                    pass
                            i += 1
                        except WindowsError:
                            break
            except Exception as e:
                self.log_status(f"[警告] 重置MAC地址时出错: {str(e)}")

            # 2. 重置CPU标识符
            self.log_status("[处理] 重置CPU标识符...")
            try:
                cpu_key = r"HARDWARE\DESCRIPTION\System\CentralProcessor\0"
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, cpu_key, 0, winreg.KEY_SET_VALUE) as key:
                    # 修改CPU标识符
                    new_cpu_id = f"GenuineIntel-{uuid.uuid4().hex[:8]}"
                    winreg.SetValueEx(key, "Identifier", 0, winreg.REG_SZ, new_cpu_id)
                    success_count += 1
                    self.log_status("[成功] CPU标识符已重置")
            except Exception as e:
                self.log_status(f"[警告] 重置CPU标识符时出错: {str(e)}")

            # 3. 重置主板序列号
            self.log_status("[处理] 重置主板序列号...")
            try:
                # 使用WMI修改主板信息
                wmi_commands = [
                    f'wmic baseboard set serialnumber="{uuid.uuid4().hex[:16].upper()}"',
                    f'wmic computersystem set model="Custom-{uuid.uuid4().hex[:8]}"'
                ]
                for cmd in wmi_commands:
                    try:
                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                        if result.returncode == 0:
                            success_count += 1
                    except:
                        pass
                self.log_status("[成功] 主板序列号重置尝试完成")
            except Exception as e:
                self.log_status(f"[警告] 重置主板序列号时出错: {str(e)}")

            # 4. 重置硬盘序列号相关注册表项
            self.log_status("[处理] 重置硬盘标识符...")
            try:
                disk_keys = [
                    r"HARDWARE\DEVICEMAP\Scsi",
                    r"SYSTEM\CurrentControlSet\Services\Disk\Enum"
                ]
                for disk_key in disk_keys:
                    try:
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, disk_key, 0, winreg.KEY_SET_VALUE) as key:
                            # 尝试修改硬盘相关标识
                            new_disk_id = f"DISK_{uuid.uuid4().hex[:16].upper()}"
                            try:
                                winreg.SetValueEx(key, "Identifier", 0, winreg.REG_SZ, new_disk_id)
                                success_count += 1
                            except:
                                pass
                    except:
                        pass
                self.log_status("[成功] 硬盘标识符重置尝试完成")
            except Exception as e:
                self.log_status(f"[警告] 重置硬盘标识符时出错: {str(e)}")

            # 5. 重置BIOS相关标识符
            self.log_status("[处理] 重置BIOS标识符...")
            try:
                bios_keys = [
                    r"HARDWARE\DESCRIPTION\System\BIOS",
                    r"SYSTEM\CurrentControlSet\Control\SystemInformation"
                ]
                for bios_key in bios_keys:
                    try:
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, bios_key, 0, winreg.KEY_SET_VALUE) as key:
                            # 修改BIOS相关信息
                            new_bios_info = {
                                "SystemManufacturer": f"Custom-{uuid.uuid4().hex[:8]}",
                                "SystemProductName": f"Model-{uuid.uuid4().hex[:8]}",
                                "BIOSVersion": f"Ver-{uuid.uuid4().hex[:8]}",
                                "SystemSKU": f"SKU-{uuid.uuid4().hex[:8]}"
                            }
                            for name, value in new_bios_info.items():
                                try:
                                    winreg.SetValueEx(key, name, 0, winreg.REG_SZ, value)
                                    success_count += 1
                                except:
                                    pass
                    except:
                        pass
                self.log_status("[成功] BIOS标识符重置尝试完成")
            except Exception as e:
                self.log_status(f"[警告] 重置BIOS标识符时出错: {str(e)}")

            if success_count > 0:
                self.log_status(f"[成功] 系统硬件标识符重置完成: {success_count} 个项目成功")
                return True
            else:
                self.log_status("[警告] 未能重置任何硬件标识符")
                return False

        except Exception as e:
            self.log_status(f"[失败] 重置系统硬件标识符时出错: {str(e)}")
            return False

    def reset_registry_telemetry_ids(self, new_mac_machine_id, new_device_id, new_sqm_id):
        """重置注册表中的遥测ID"""
        try:
            success_count = 0

            # 可能的注册表位置
            registry_paths = [
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\VSCode"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Cursor"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\VSCode"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Cursor"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Cursor"),
            ]

            for hkey, subkey in registry_paths:
                try:
                    with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE | winreg.KEY_QUERY_VALUE) as key:
                        # 尝试设置遥测ID
                        try:
                            winreg.SetValueEx(key, "telemetry.macMachineId", 0, winreg.REG_SZ, new_mac_machine_id)
                            success_count += 1
                        except:
                            pass
                        try:
                            winreg.SetValueEx(key, "telemetry.devDeviceId", 0, winreg.REG_SZ, new_device_id)
                            success_count += 1
                        except:
                            pass
                        try:
                            winreg.SetValueEx(key, "telemetry.sqmId", 0, winreg.REG_SZ, new_sqm_id)
                            success_count += 1
                        except:
                            pass
                except:
                    # 注册表项不存在或无权限，跳过
                    continue

            if success_count > 0:
                self.log_status(f"[成功] 注册表中 {success_count} 个遥测ID已重置")
                return True
            else:
                self.log_status("[信息] 注册表中未找到遥测ID项")
                return True  # 不存在也算成功

        except Exception as e:
            self.log_status(f"[警告] 重置注册表遥测ID时出错: {str(e)}")
            return False

    def reset_config_files_telemetry_ids(self, new_mac_machine_id, new_device_id, new_sqm_id):
        """重置其他配置文件中的遥测ID"""
        try:
            success_count = 0

            # 可能的配置文件位置
            config_files = [
                os.path.join(self.cursor_appdata_path, 'User', 'settings.json'),
                os.path.join(self.cursor_appdata_path, 'User', 'globalState.json'),
                os.path.join(self.cursor_appdata_path, 'User', 'workspaceStorage', 'state.vscdb'),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Cursor', 'User', 'globalStorage', 'storage.json'),
                os.path.join(os.environ.get('USERPROFILE', ''), '.cursor', 'storage.json'),
            ]

            for config_file in config_files:
                if os.path.exists(config_file) and config_file.endswith('.json'):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        # 更新遥测ID
                        updated = False
                        if 'telemetry.macMachineId' in data:
                            data['telemetry.macMachineId'] = new_mac_machine_id
                            updated = True
                        if 'telemetry.devDeviceId' in data:
                            data['telemetry.devDeviceId'] = new_device_id
                            updated = True
                        if 'telemetry.sqmId' in data:
                            data['telemetry.sqmId'] = new_sqm_id
                            updated = True

                        if updated:
                            with open(config_file, 'w', encoding='utf-8') as f:
                                json.dump(data, f, indent=2, ensure_ascii=False)
                            success_count += 1
                            self.log_status(f"[成功] 已更新配置文件: {os.path.basename(config_file)}")
                    except:
                        # 文件格式错误或无权限，跳过
                        continue

            if success_count > 0:
                self.log_status(f"[成功] {success_count} 个配置文件中的遥测ID已重置")
            else:
                self.log_status("[信息] 其他配置文件中未找到遥测ID")

            return True

        except Exception as e:
            self.log_status(f"[警告] 重置配置文件遥测ID时出错: {str(e)}")
            return False

    def reset_wmi_hardware_fingerprint(self):
        """重置WMI硬件指纹信息"""
        try:
            success_count = 0

            # WMI命令列表 - 修改各种硬件标识符
            wmi_commands = [
                # 修改主板信息
                f'wmic baseboard set serialnumber="{uuid.uuid4().hex[:16].upper()}"',
                f'wmic baseboard set product="MB-{uuid.uuid4().hex[:8]}"',

                # 修改计算机系统信息
                f'wmic computersystem set model="PC-{uuid.uuid4().hex[:8]}"',
                f'wmic computersystem set manufacturer="Custom-{uuid.uuid4().hex[:6]}"',

                # 修改BIOS信息
                f'wmic bios set serialnumber="{uuid.uuid4().hex[:12].upper()}"',
                f'wmic bios set version="Ver-{uuid.uuid4().hex[:8]}"',

                # 修改CPU信息
                f'wmic cpu set processorid="{uuid.uuid4().hex[:16].upper()}"',

                # 修改内存信息
                f'wmic memorychip set serialnumber="{uuid.uuid4().hex[:12].upper()}"',

                # 修改硬盘信息
                f'wmic diskdrive set serialnumber="{uuid.uuid4().hex[:16].upper()}"',

                # 修改网卡信息
                f'wmic path win32_networkadapter set pnpdeviceid="PCI\\VEN_{uuid.uuid4().hex[:4].upper()}&DEV_{uuid.uuid4().hex[:4].upper()}"'
            ]

            self.log_status("[处理] 开始修改WMI硬件信息...")

            for i, cmd in enumerate(wmi_commands):
                try:
                    self.log_status(f"[处理] 执行WMI命令 {i+1}/{len(wmi_commands)}...")
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
                    if result.returncode == 0 or "successfully" in result.stdout.lower():
                        success_count += 1
                        self.log_status(f"[成功] WMI命令 {i+1} 执行成功")
                    else:
                        self.log_status(f"[警告] WMI命令 {i+1} 可能失败: {result.stderr[:50]}")
                except subprocess.TimeoutExpired:
                    self.log_status(f"[警告] WMI命令 {i+1} 超时")
                except Exception as e:
                    self.log_status(f"[警告] WMI命令 {i+1} 异常: {str(e)[:50]}")

            # 额外的注册表修改 - 直接修改WMI相关的注册表项
            self.log_status("[处理] 修改WMI注册表项...")
            try:
                wmi_registry_keys = [
                    (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Control\WMI\Autologger"),
                    (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows NT\CurrentVersion"),
                    (winreg.HKEY_LOCAL_MACHINE, r"HARDWARE\DESCRIPTION\System")
                ]

                for hkey, subkey in wmi_registry_keys:
                    try:
                        with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_SET_VALUE) as key:
                            # 修改一些标识符
                            identifiers = {
                                "SystemBiosVersion": f"BIOS-{uuid.uuid4().hex[:8]}",
                                "SystemManufacturer": f"Mfg-{uuid.uuid4().hex[:6]}",
                                "SystemProductName": f"Prod-{uuid.uuid4().hex[:8]}",
                                "SystemFamily": f"Fam-{uuid.uuid4().hex[:6]}",
                                "SystemSKU": f"SKU-{uuid.uuid4().hex[:8]}",
                                "SystemVersion": f"Ver-{uuid.uuid4().hex[:6]}"
                            }

                            for name, value in identifiers.items():
                                try:
                                    winreg.SetValueEx(key, name, 0, winreg.REG_SZ, value)
                                    success_count += 1
                                except:
                                    pass
                    except:
                        pass
            except Exception as e:
                self.log_status(f"[警告] 修改WMI注册表时出错: {str(e)}")

            # 重置Windows激活相关的硬件哈希
            self.log_status("[处理] 重置Windows激活硬件哈希...")
            try:
                activation_commands = [
                    "slmgr /rearm",  # 重置激活宽限期
                    "slmgr /dlv",    # 显示详细许可信息
                ]

                for cmd in activation_commands:
                    try:
                        subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
                        success_count += 1
                    except:
                        pass
            except Exception as e:
                self.log_status(f"[警告] 重置激活信息时出错: {str(e)}")

            if success_count > 0:
                self.log_status(f"[成功] WMI硬件指纹重置完成: {success_count} 个项目成功")
                self.log_status("[重要] 建议重启计算机以使WMI更改生效")
                return True
            else:
                self.log_status("[警告] 未能重置任何WMI硬件指纹")
                return False

        except Exception as e:
            self.log_status(f"[失败] 重置WMI硬件指纹时出错: {str(e)}")
            return False

    def clear_telemetry_cache(self):
        """清除可能缓存遥测ID的所有位置"""
        try:
            success_count = 0

            # 1. 清除Windows临时文件中的Cursor相关文件
            temp_dirs = [
                os.environ.get('TEMP', ''),
                os.environ.get('TMP', ''),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Temp'),
                os.path.join(os.environ.get('USERPROFILE', ''), 'AppData', 'Local', 'Temp'),
            ]

            for temp_dir in temp_dirs:
                if temp_dir and os.path.exists(temp_dir):
                    try:
                        # 查找Cursor相关的临时文件
                        for item in os.listdir(temp_dir):
                            if 'cursor' in item.lower() or 'vscode' in item.lower():
                                item_path = os.path.join(temp_dir, item)
                                try:
                                    if os.path.isfile(item_path):
                                        os.remove(item_path)
                                        success_count += 1
                                    elif os.path.isdir(item_path):
                                        shutil.rmtree(item_path, ignore_errors=True)
                                        success_count += 1
                                except:
                                    pass
                    except:
                        pass

            # 2. 清除注册表中的遥测缓存
            registry_cache_paths = [
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\UserAssist"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\RecentDocs"),
            ]

            for hkey, subkey in registry_cache_paths:
                try:
                    with winreg.OpenKey(hkey, subkey, 0, winreg.KEY_ENUMERATE_SUB_KEYS) as key:
                        # 枚举子键，查找Cursor相关的条目
                        i = 0
                        while True:
                            try:
                                subkey_name = winreg.EnumKey(key, i)
                                if 'cursor' in subkey_name.lower():
                                    try:
                                        winreg.DeleteKey(key, subkey_name)
                                        success_count += 1
                                    except:
                                        pass
                                i += 1
                            except WindowsError:
                                break
                except:
                    pass

            # 3. 清除系统事件日志中的Cursor条目（如果可能）
            try:
                # 使用wevtutil清除应用程序日志中的Cursor相关条目
                subprocess.run('wevtutil cl Application', shell=True, capture_output=True)
                success_count += 1
            except:
                pass

            # 4. 清除预取文件中的Cursor条目
            prefetch_dir = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Prefetch')
            if os.path.exists(prefetch_dir):
                try:
                    for item in os.listdir(prefetch_dir):
                        if 'cursor' in item.lower():
                            try:
                                os.remove(os.path.join(prefetch_dir, item))
                                success_count += 1
                            except:
                                pass
                except:
                    pass

            # 5. 清除用户配置文件中的其他可能位置
            other_locations = [
                os.path.join(os.environ.get('USERPROFILE', ''), '.vscode'),
                os.path.join(os.environ.get('USERPROFILE', ''), '.cursor'),
                os.path.join(os.environ.get('APPDATA', ''), 'Code'),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Programs', 'cursor'),
            ]

            for location in other_locations:
                if os.path.exists(location):
                    try:
                        # 查找遥测相关文件
                        for root, dirs, files in os.walk(location):
                            for file in files:
                                if any(keyword in file.lower() for keyword in ['telemetry', 'machine', 'device', 'sqm']):
                                    try:
                                        os.remove(os.path.join(root, file))
                                        success_count += 1
                                    except:
                                        pass
                    except:
                        pass

            if success_count > 0:
                self.log_status(f"[成功] 已清除 {success_count} 个缓存项")
            else:
                self.log_status("[信息] 未找到需要清除的缓存项")

            return True

        except Exception as e:
            self.log_status(f"[警告] 清除遥测缓存时出错: {str(e)}")
            return False


    def check_admin_privileges(self):
        """检查是否具有管理员权限"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def request_admin_privileges(self):
        """请求管理员权限"""
        if self.is_admin:
            return True

        try:
            # 以管理员身份重新启动程序
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            self.root.quit()
            return False
        except Exception as e:
            messagebox.showerror("权限错误", f"无法获取管理员权限: {str(e)}")
            return False
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_text = "Cursor机器码管理器 - 管理员模式" if self.is_admin else "Cursor机器码管理器"
        title_label = ttk.Label(main_frame, text=title_text,
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 机器码信息框架
        codes_frame = ttk.LabelFrame(main_frame, text="当前机器码信息", padding="10")
        codes_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        codes_frame.columnconfigure(1, weight=1)
        
        # 主机器ID
        ttk.Label(codes_frame, text="主机器ID:", font=("Arial", 10, "bold")).grid(
            row=0, column=0, sticky=tk.W, pady=5)
        self.main_id_var = tk.StringVar()
        self.main_id_entry = ttk.Entry(codes_frame, textvariable=self.main_id_var, 
                                      state="readonly", font=("Courier", 9))
        self.main_id_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 遥测机器ID
        ttk.Label(codes_frame, text="遥测机器ID:", font=("Arial", 10, "bold")).grid(
            row=1, column=0, sticky=tk.W, pady=5)
        self.telemetry_id_var = tk.StringVar()
        self.telemetry_id_entry = ttk.Entry(codes_frame, textvariable=self.telemetry_id_var, 
                                           state="readonly", font=("Courier", 9))
        self.telemetry_id_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # Mac机器ID
        ttk.Label(codes_frame, text="Mac机器ID:", font=("Arial", 10, "bold")).grid(
            row=2, column=0, sticky=tk.W, pady=5)
        self.mac_id_var = tk.StringVar()
        self.mac_id_entry = ttk.Entry(codes_frame, textvariable=self.mac_id_var, 
                                     state="readonly", font=("Courier", 9))
        self.mac_id_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 设备ID
        ttk.Label(codes_frame, text="设备ID:", font=("Arial", 10, "bold")).grid(
            row=3, column=0, sticky=tk.W, pady=5)
        self.device_id_var = tk.StringVar()
        self.device_id_entry = ttk.Entry(codes_frame, textvariable=self.device_id_var, 
                                        state="readonly", font=("Courier", 9))
        self.device_id_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # SQM ID
        ttk.Label(codes_frame, text="SQM ID:", font=("Arial", 10, "bold")).grid(
            row=4, column=0, sticky=tk.W, pady=5)
        self.sqm_id_var = tk.StringVar()
        self.sqm_id_entry = ttk.Entry(codes_frame, textvariable=self.sqm_id_var,
                                     state="readonly", font=("Courier", 9))
        self.sqm_id_entry.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # 系统MachineGuid
        ttk.Label(codes_frame, text="系统MachineGuid:", font=("Arial", 10, "bold")).grid(
            row=5, column=0, sticky=tk.W, pady=5)
        self.system_guid_var = tk.StringVar()
        self.system_guid_entry = ttk.Entry(codes_frame, textvariable=self.system_guid_var,
                                          state="readonly", font=("Courier", 9))
        self.system_guid_entry.grid(row=5, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=15)

        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.grid(row=0, column=0, columnspan=4, pady=(0, 10))

        # 刷新按钮
        self.refresh_button = ttk.Button(button_row1, text="刷新机器码",
                                        command=self.refresh_machine_codes)
        self.refresh_button.grid(row=0, column=0, padx=(0, 10))

        # 诊断按钮
        self.diagnose_button = ttk.Button(button_row1, text="诊断问题",
                                         command=self.diagnose_issues)
        self.diagnose_button.grid(row=0, column=1, padx=(0, 10))

        # 权限修复按钮
        self.fix_permission_button = ttk.Button(button_row1, text="修复权限",
                                               command=self.fix_permission_issues)
        self.fix_permission_button.grid(row=0, column=2, padx=(0, 10))

        # 终极删除按钮（移到第一行）
        self.ultimate_delete_button = ttk.Button(button_row1, text="终极删除",
                                                command=self.ultimate_delete_cursor,
                                                style="Accent.TButton")
        self.ultimate_delete_button.grid(row=0, column=3, padx=(0, 10))

        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.grid(row=1, column=0, columnspan=4)

        # 管理员权限按钮
        self.admin_button = ttk.Button(button_row2, text="获取管理员权限",
                                      command=self.request_admin_privileges)
        self.admin_button.grid(row=0, column=0)
        
        # 权限状态显示
        self.admin_status_frame = ttk.Frame(main_frame)
        self.admin_status_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))

        self.admin_status_label = ttk.Label(self.admin_status_frame, text="",
                                           font=("Arial", 10, "bold"))
        self.admin_status_label.grid(row=0, column=0)

        # 状态信息显示框
        status_frame = ttk.LabelFrame(main_frame, text="状态信息与操作日志", padding="10")
        status_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(0, weight=1)

        self.status_text = scrolledtext.ScrolledText(status_frame, height=15, state="disabled",
                                                    font=("Consolas", 9))
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置行权重
        main_frame.rowconfigure(4, weight=1)
    
    def refresh_machine_codes(self):
        """刷新机器码显示"""
        machine_codes = self.get_cursor_machine_codes()
        
        # 更新界面显示
        self.main_id_var.set(machine_codes['main_machine_id'])
        self.telemetry_id_var.set(machine_codes['telemetry_machine_id'])
        self.mac_id_var.set(machine_codes['telemetry_mac_machine_id'])
        self.device_id_var.set(machine_codes['telemetry_dev_device_id'])
        self.sqm_id_var.set(machine_codes['telemetry_sqm_id'])

        # 更新系统MachineGuid显示
        current_system_guid = self.get_system_machine_guid()
        self.system_guid_var.set(current_system_guid)
        
        # 更新状态信息
        self.update_status_info(machine_codes)
    
    def update_status_info(self, machine_codes):
        """更新状态信息显示"""
        self.status_text.config(state="normal")
        self.status_text.delete(1.0, tk.END)
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        admin_status = "[管理员模式]" if self.is_admin else "[普通模式]"

        status_info = f"""=== Cursor机器码状态 ({timestamp}) ===

运行模式: {admin_status}
Cursor安装状态: {'已安装' if machine_codes['cursor_installed'] else '未安装'}
配置目录: {self.cursor_appdata_path}

机器码详情:
|- 主机器ID: {machine_codes['main_machine_id']}
|- 遥测机器ID: {machine_codes['telemetry_machine_id']}
|- Mac机器ID: {machine_codes['telemetry_mac_machine_id']}
|- 设备ID: {machine_codes['telemetry_dev_device_id']}
|- SQM ID: {machine_codes['telemetry_sqm_id']}
|- 系统MachineGuid: {self.get_system_machine_guid()}

说明:
* 主机器ID: Cursor的主要机器标识符
* 遥测机器ID: 用于使用统计和遥测的机器标识
* Mac机器ID: Mac平台特定的机器标识
* 设备ID: 设备唯一标识符 (可单独重置)
* SQM ID: 软件质量监控标识符
* 系统MachineGuid: Windows系统级机器标识符 (可单独重置)

[重要提示]
程序已以管理员身份运行，具有完整的系统权限。
* "终极删除": 重置所有ID + 使用多种方法强制删除Cursor数据
"""
        
        self.status_text.insert(1.0, status_info)
        self.status_text.config(state="disabled")

    def update_admin_status(self):
        """更新管理员权限状态显示"""
        if self.is_admin:
            self.admin_status_label.config(text="[管理员] 程序已以管理员身份运行", foreground="green")
            self.admin_button.config(state="disabled", text="管理员权限已激活")
        else:
            # 这种情况理论上不应该发生，因为程序启动时会强制要求管理员权限
            self.admin_status_label.config(text="[警告] 权限检查异常", foreground="red")
            self.admin_button.config(state="normal", text="重新获取权限")

    def diagnose_issues(self):
        """诊断Cursor重置可能遇到的问题"""
        self.log_status("[搜索] 开始诊断Cursor重置问题...")

        issues = []

        # 1. 检查Cursor目录是否存在
        if not os.path.exists(self.cursor_appdata_path):
            self.log_status("[信息] Cursor配置目录不存在，无需重置")
            messagebox.showinfo("诊断结果", "Cursor配置目录不存在，无需重置。")
            return

        self.log_status(f"[成功] Cursor配置目录存在: {self.cursor_appdata_path}")

        # 2. 检查Cursor进程
        if self.is_cursor_running():
            issues.append("CURSOR_RUNNING")
            self.log_status("[失败] 检测到Cursor进程正在运行")
        else:
            self.log_status("[成功] Cursor进程检查通过")

        # 3. 检查管理员权限
        if not self.is_admin:
            issues.append("NO_ADMIN")
            self.log_status("[失败] 当前无管理员权限")
        else:
            self.log_status("[成功] 具有管理员权限")

        # 4. 检查文件权限
        if not self.check_directory_permissions():
            issues.append("PERMISSION_DENIED")
            self.log_status("[失败] 检测到目录权限问题")
        else:
            self.log_status("[成功] 目录权限检查通过")

        # 5. 检查文件锁定状态
        locked_files = self.check_locked_files()
        if locked_files:
            issues.append("FILES_LOCKED")
            self.log_status(f"[失败] 检测到被锁定的文件: {len(locked_files)}个")
            for file in locked_files[:3]:  # 只显示前3个
                self.log_status(f"   [锁定] {os.path.basename(file)}")
            if len(locked_files) > 3:
                self.log_status(f"   ... 还有 {len(locked_files) - 3} 个文件")
        else:
            self.log_status("[成功] 文件锁定检查通过")

        # 6. 检查只读文件
        readonly_files = self.check_readonly_files()
        if readonly_files:
            issues.append("READONLY_FILES")
            self.log_status(f"[警告] 检测到只读文件: {len(readonly_files)}个")
        else:
            self.log_status("[成功] 只读文件检查通过")

        # 显示诊断结果
        if not issues:
            self.log_status("[完成] 诊断完成：未发现问题，可以正常重置")
            messagebox.showinfo("诊断结果", "未发现问题，可以正常进行重置操作。")
        else:
            self.log_status(f"[警告] 诊断完成：发现 {len(issues)} 个问题")
            issue_names = {
                "CURSOR_RUNNING": "Cursor进程正在运行",
                "NO_ADMIN": "缺少管理员权限",
                "PERMISSION_DENIED": "目录权限不足",
                "FILES_LOCKED": "文件被锁定",
                "READONLY_FILES": "存在只读文件"
            }

            problem_list = [issue_names.get(issue, issue) for issue in issues]
            messagebox.showwarning("诊断结果",
                                 f"发现以下问题：\n\n" +
                                 "\n".join([f"• {problem}" for problem in problem_list]) +
                                 "\n\n建议点击'修复权限'按钮自动修复这些问题。")

    def check_directory_permissions(self):
        """检查目录权限"""
        try:
            # 尝试在目录中创建临时文件
            test_file = os.path.join(self.cursor_appdata_path, 'test_permission.tmp')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            return True
        except:
            return False

    def check_locked_files(self):
        """检查被锁定的文件"""
        locked_files = []

        try:
            for root, dirs, files in os.walk(self.cursor_appdata_path):
                for file in files[:10]:  # 限制检查数量，避免太慢
                    file_path = os.path.join(root, file)
                    try:
                        # 尝试重命名文件来检查是否被锁定
                        temp_name = file_path + '.tmp'
                        os.rename(file_path, temp_name)
                        os.rename(temp_name, file_path)
                    except:
                        locked_files.append(file_path)
        except:
            pass

        return locked_files

    def check_readonly_files(self):
        """检查只读文件"""
        readonly_files = []

        try:
            for root, dirs, files in os.walk(self.cursor_appdata_path):
                for file in files[:20]:  # 限制检查数量
                    file_path = os.path.join(root, file)
                    try:
                        if not os.access(file_path, os.W_OK):
                            readonly_files.append(file_path)
                    except:
                        pass
        except:
            pass

        return readonly_files

    def fix_permission_issues(self):
        """修复权限问题"""
        if not os.path.exists(self.cursor_appdata_path):
            messagebox.showinfo("提示", "Cursor配置目录不存在，无需修复。")
            return

        # 检查管理员权限
        if not self.is_admin:
            result = messagebox.askyesno("需要管理员权限",
                                       "修复权限需要管理员权限。\n是否以管理员身份重新启动程序？")
            if result:
                self.request_admin_privileges()
            return

        self.log_status("[处理] 权限修复开始...")

        try:
            # 步骤1: 结束Cursor进程
            self.log_status("[处理] 步骤1: 结束Cursor相关进程...")
            self.kill_cursor_processes()

            # 步骤2: 修改文件权限
            self.log_status("[处理] 步骤2: 修改文件权限...")
            self.fix_file_permissions()

            # 步骤3: 移除只读属性
            self.log_status("[处理] 步骤3: 移除只读属性...")
            self.remove_readonly_attributes()

            # 步骤4: 验证修复结果
            self.log_status("[处理] 步骤4: 验证修复结果...")
            if self.check_directory_permissions():
                self.log_status("[成功] 权限修复成功！")
                messagebox.showinfo("修复成功", "权限问题已修复，现在可以正常进行重置操作。")
            else:
                self.log_status("[警告] 权限修复可能不完整")
                messagebox.showwarning("修复警告", "权限修复可能不完整，建议尝试强制重置。")

        except Exception as e:
            error_msg = f"权限修复失败: {str(e)}"
            self.log_status(f"[失败] {error_msg}")
            messagebox.showerror("修复失败", error_msg)

    def fix_file_permissions(self):
        """修复文件权限"""
        try:
            # 使用icacls命令修改权限
            cmd = f'icacls "{self.cursor_appdata_path}" /grant Administrators:F /t'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                self.log_status("[成功] 管理员权限设置成功")
            else:
                self.log_status(f"[警告] 管理员权限设置警告: {result.stderr}")

            # 给当前用户权限
            cmd2 = f'icacls "{self.cursor_appdata_path}" /grant %username%:F /t'
            result2 = subprocess.run(cmd2, shell=True, capture_output=True, text=True)

            if result2.returncode == 0:
                self.log_status("[成功] 用户权限设置成功")
            else:
                self.log_status(f"[警告] 用户权限设置警告: {result2.stderr}")

        except Exception as e:
            self.log_status(f"[失败] 权限设置失败: {str(e)}")
            raise

    def remove_readonly_attributes(self):
        """移除只读属性"""
        try:
            cmd = f'attrib -r "{self.cursor_appdata_path}\\*.*" /s'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                self.log_status("[成功] 只读属性移除成功")
            else:
                self.log_status(f"[警告] 移除只读属性警告: {result.stderr}")

        except Exception as e:
            self.log_status(f"[失败] 移除只读属性失败: {str(e)}")
            raise



    def method_force_system_delete(self):
        """方法5: 强制系统删除"""
        try:
            # 使用takeown获取所有权
            cmd1 = f'takeown /f "{self.cursor_appdata_path}" /r /d y'
            subprocess.run(cmd1, shell=True, capture_output=True, text=True)

            # 再次设置权限
            cmd2 = f'icacls "{self.cursor_appdata_path}" /grant administrators:F /t'
            subprocess.run(cmd2, shell=True, capture_output=True, text=True)

            # 强制删除
            cmd3 = f'rmdir /s /q "{self.cursor_appdata_path}"'
            result = subprocess.run(cmd3, shell=True, capture_output=True, text=True)

            return result.returncode == 0 and not os.path.exists(self.cursor_appdata_path)
        except:
            return False
    


    def force_remove_directory(self, path):
        """强制删除目录，处理各种权限和锁定问题"""
        try:
            # 方法1: 直接删除
            self.log_status("[处理] 尝试直接删除...")
            shutil.rmtree(path)
            self.log_status("[成功] 直接删除成功")
            return True

        except PermissionError as e:
            self.log_status(f"[警告] 权限错误: {str(e)}")
            return self.handle_permission_error(path)

        except OSError as e:
            self.log_status(f"[警告] 系统错误: {str(e)}")
            return self.handle_os_error(path, e)

        except Exception as e:
            self.log_status(f"[失败] 未知错误: {str(e)}")
            return False

    def handle_permission_error(self, path):
        """处理权限错误"""
        try:
            # 方法2: 修改文件属性后删除
            self.log_status("[处理] 尝试修改文件属性...")
            self.remove_readonly_and_delete(path)
            self.log_status("[成功] 修改属性后删除成功")
            return True

        except Exception as e:
            self.log_status(f"[警告] 修改属性失败: {str(e)}")

            # 方法3: 使用系统命令强制删除
            return self.force_delete_with_cmd(path)

    def handle_os_error(self, path, error):
        """处理操作系统错误"""
        if "being used by another process" in str(error) or "另一个程序正在使用" in str(error):
            self.log_status("[警告] 文件被其他程序占用")

            # 尝试结束可能的Cursor相关进程
            self.kill_cursor_processes()

            # 等待一下再重试
            import time
            time.sleep(2)

            try:
                shutil.rmtree(path)
                self.log_status("[成功] 结束进程后删除成功")
                return True
            except:
                return self.force_delete_with_cmd(path)
        else:
            return self.force_delete_with_cmd(path)

    def remove_readonly_and_delete(self, path):
        """移除只读属性并删除"""
        import stat

        def handle_remove_readonly(func, path, exc):
            if os.path.exists(path):
                os.chmod(path, stat.S_IWRITE)
                func(path)

        shutil.rmtree(path, onerror=handle_remove_readonly)

    def force_delete_with_cmd(self, path):
        """使用系统命令强制删除"""
        try:
            self.log_status("[处理] 尝试使用系统命令删除...")

            # 使用rmdir命令强制删除
            cmd = f'rmdir /s /q "{path}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                self.log_status("[成功] 系统命令删除成功")
                return True
            else:
                self.log_status(f"[失败] 系统命令失败: {result.stderr}")

                # 最后尝试: 建议用户手动删除
                self.suggest_manual_deletion(path)
                return False

        except Exception as e:
            self.log_status(f"[失败] 系统命令执行失败: {str(e)}")
            self.suggest_manual_deletion(path)
            return False

    def kill_cursor_processes(self):
        """结束所有Cursor相关进程"""
        try:
            self.log_status("[处理] 正在结束Cursor相关进程...")

            # 扩展的进程列表
            processes = [
                'Cursor.exe', 'cursor.exe', 'Code.exe', 'code.exe',
                'CursorHelper.exe', 'cursor-helper.exe',
                'node.exe'  # Cursor可能使用的Node.js进程
            ]

            killed_count = 0
            for process in processes:
                try:
                    # 先尝试温和结束
                    result1 = subprocess.run(f'taskkill /im {process}',
                                           shell=True, capture_output=True, text=True)
                    if result1.returncode == 0:
                        killed_count += 1
                        self.log_status(f"[成功] 温和结束进程: {process}")
                        continue

                    # 再尝试强制结束
                    result2 = subprocess.run(f'taskkill /f /im {process}',
                                           shell=True, capture_output=True, text=True)
                    if result2.returncode == 0:
                        killed_count += 1
                        self.log_status(f"[成功] 强制结束进程: {process}")

                except Exception as e:
                    self.log_status(f"[警告] 结束进程 {process} 时出错: {str(e)}")

            if killed_count > 0:
                self.log_status(f"[成功] 成功结束 {killed_count} 个进程")
                time.sleep(1)  # 等待进程完全结束
            else:
                self.log_status("[信息] 未发现需要结束的Cursor进程")

        except Exception as e:
            self.log_status(f"[失败] 结束进程时发生异常: {str(e)}")

    def suggest_manual_deletion(self, path):
        """尝试终极删除方法，避免手动操作"""
        self.log_status("[启动] 启动终极删除模式...")

        # 尝试更多强力删除方法
        ultimate_methods = [
            ("PowerShell强制删除", self.method_powershell_delete),
            ("批处理脚本删除", self.method_batch_delete),
            ("逐个文件删除", self.method_individual_file_delete),
            ("系统工具删除", self.method_system_tools_delete),
            ("重启后删除", self.method_pending_delete)
        ]

        for method_name, method_func in ultimate_methods:
            self.log_status(f"[处理] 尝试终极方法: {method_name}")
            try:
                if method_func(path):
                    self.log_status(f"[成功] {method_name} 成功！")
                    return True
                else:
                    self.log_status(f"[失败] {method_name} 失败")
            except Exception as e:
                self.log_status(f"[失败] {method_name} 异常: {str(e)}")

        # 如果所有方法都失败，显示最终提示
        self.log_status("[失败] 所有自动删除方法都失败")
        self.log_status("[提示] 最终解决方案:")
        self.log_status("   1. 重启计算机")
        self.log_status("   2. 重启后立即运行此程序")
        self.log_status("   3. 或在安全模式下删除")

        result = messagebox.askyesno(
            "需要重启解决",
            f"所有自动删除方法都失败了。\n\n"
            f"建议解决方案：\n"
            f"1. 重启计算机\n"
            f"2. 重启后立即运行此程序重试\n"
            f"3. 或者现在打开文件夹手动删除\n\n"
            f"是否现在重启计算机？\n"
            f"(选择'否'将打开文件夹)"
        )

        if result:
            # 用户选择重启
            try:
                self.log_status("[处理] 准备重启计算机...")
                subprocess.run("shutdown /r /t 10 /c \"重启后请重新运行Cursor机器码管理器\"", shell=True)
                self.log_status("[成功] 重启命令已发送，10秒后重启")
                messagebox.showinfo("重启提示", "计算机将在10秒后重启。\n重启后请重新运行此程序。")
            except Exception as e:
                self.log_status(f"[失败] 重启失败: {str(e)}")
                messagebox.showerror("重启失败", f"无法自动重启: {str(e)}\n请手动重启计算机。")
        else:
            # 用户选择打开文件夹
            try:
                parent_dir = os.path.dirname(path)
                subprocess.run(f'explorer "{parent_dir}"', shell=True)
                self.log_status("[文件夹] 已打开文件夹，请手动删除Cursor文件夹")
            except Exception as e:
                self.log_status(f"[失败] 无法打开文件夹: {str(e)}")

        return False

    def method_powershell_delete(self, path):
        """PowerShell强制删除方法"""
        try:
            # 使用PowerShell的Remove-Item命令强制删除
            ps_script = f'''
            $path = "{path}"
            if (Test-Path $path) {{
                Get-ChildItem $path -Recurse | ForEach-Object {{
                    $_.Attributes = "Normal"
                }}
                Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
            }}
            '''

            result = subprocess.run(['powershell', '-Command', ps_script],
                                  capture_output=True, text=True, shell=True)

            return not os.path.exists(path)
        except:
            return False

    def method_batch_delete(self, path):
        """批处理脚本删除方法"""
        try:
            # 创建临时批处理文件
            batch_content = f'''@echo off
chcp 65001 >nul
echo 正在强制删除Cursor目录...

:: 结束相关进程
taskkill /f /im Cursor.exe >nul 2>&1
taskkill /f /im cursor.exe >nul 2>&1
taskkill /f /im Code.exe >nul 2>&1

:: 获取所有权
takeown /f "{path}" /r /d y >nul 2>&1

:: 设置权限
icacls "{path}" /grant administrators:F /t >nul 2>&1
icacls "{path}" /grant %username%:F /t >nul 2>&1

:: 移除只读属性
attrib -r "{path}\\*.*" /s >nul 2>&1

:: 强制删除
rmdir /s /q "{path}" >nul 2>&1

:: 清理临时文件
del "%~f0" >nul 2>&1
'''

            batch_file = os.path.join(os.environ['TEMP'], 'delete_cursor.bat')
            with open(batch_file, 'w', encoding='utf-8') as f:
                f.write(batch_content)

            # 以管理员身份运行批处理文件
            subprocess.run(f'"{batch_file}"', shell=True)
            time.sleep(3)  # 等待执行完成

            return not os.path.exists(path)
        except:
            return False

    def method_individual_file_delete(self, path):
        """逐个文件删除方法"""
        try:
            self.log_status("[处理] 开始逐个删除文件...")
            deleted_count = 0

            # 从最深层开始删除
            for root, dirs, files in os.walk(path, topdown=False):
                # 删除文件
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        # 移除只读属性
                        os.chmod(file_path, stat.S_IWRITE)
                        # 删除文件
                        os.remove(file_path)
                        deleted_count += 1
                        if deleted_count % 50 == 0:
                            self.log_status(f"   已删除 {deleted_count} 个文件...")
                    except:
                        # 尝试系统命令删除
                        try:
                            subprocess.run(f'del /f /q "{file_path}"', shell=True, capture_output=True)
                        except:
                            pass

                # 删除空目录
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    try:
                        os.rmdir(dir_path)
                    except:
                        try:
                            subprocess.run(f'rmdir /q "{dir_path}"', shell=True, capture_output=True)
                        except:
                            pass

            # 最后删除根目录
            try:
                os.rmdir(path)
            except:
                subprocess.run(f'rmdir /q "{path}"', shell=True, capture_output=True)

            self.log_status(f"[成功] 逐个删除完成，共删除 {deleted_count} 个文件")
            return not os.path.exists(path)
        except:
            return False

    def method_system_tools_delete(self, path):
        """系统工具删除方法"""
        try:
            # 使用sdelete工具（如果可用）
            sdelete_commands = [
                f'sdelete -p 1 -r -s -z "{path}"',
                f'sdelete -p 1 -r "{path}"'
            ]

            for cmd in sdelete_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
                    if not os.path.exists(path):
                        return True
                except:
                    continue

            # 使用cipher命令覆盖删除
            try:
                subprocess.run(f'cipher /w:"{os.path.dirname(path)}"',
                             shell=True, capture_output=True, timeout=60)
            except:
                pass

            return not os.path.exists(path)
        except:
            return False

    def method_pending_delete(self, path):
        """重启后删除方法"""
        try:
            # 使用MoveFileEx API设置重启后删除
            import ctypes
            from ctypes import wintypes

            # 定义常量
            MOVEFILE_DELAY_UNTIL_REBOOT = 0x4

            # 加载kernel32.dll
            kernel32 = ctypes.windll.kernel32

            # 设置重启后删除
            result = kernel32.MoveFileExW(
                ctypes.c_wchar_p(path),
                None,
                MOVEFILE_DELAY_UNTIL_REBOOT
            )

            if result:
                self.log_status("[成功] 已设置重启后删除")
                return True
            else:
                return False

        except Exception as e:
            self.log_status(f"[失败] 设置重启后删除失败: {str(e)}")
            return False

    def ultimate_delete_cursor(self):
        """终极删除Cursor（使用所有可能的方法）并重置所有ID"""
        # 确认对话框
        result = messagebox.askyesno(
            "确认终极删除",
            "终极删除将执行以下操作：\n\n"
            "1. 重置 Mac机器ID\n"
            "2. 重置 设备ID\n"
            "3. 重置 SQMID\n"
            "4. 重置系统 MachineGuid\n"
            "5. 使用所有可能的方法删除Cursor数据\n"
            "   (PowerShell、批处理、逐个文件删除等)\n\n"
            "如果删除失败，程序会自动设置重启后删除，\n"
            "或提供重启选项来彻底解决问题。\n\n"
            "此操作需要管理员权限。\n\n"
            "是否继续？",
            icon="warning"
        )

        if not result:
            return

        self.log_status("[启动] 启动终极删除模式...")

        # 步骤0: 结束Cursor进程（确保文件不被占用）
        self.log_status("[处理] 步骤0: 结束Cursor进程...")
        self.kill_cursor_processes()

        # 步骤1: 重置系统MachineGuid（在删除文件之前）
        self.log_status("[处理] 步骤1: 重置系统MachineGuid...")
        system_guid_reset_success = self.reset_system_machine_guid()

        # 步骤2: 重置所有遥测ID（在删除文件之前）
        self.log_status("[处理] 步骤2: 重置所有遥测ID...")
        telemetry_reset_success = self.reset_all_telemetry_ids()

        # 步骤2.5: 重置WMI硬件指纹
        self.log_status("[处理] 步骤2.5: 重置WMI硬件指纹...")
        wmi_reset_success = self.reset_wmi_hardware_fingerprint()

        # 步骤2.6: 清除可能的缓存和临时文件
        self.log_status("[处理] 步骤2.6: 清除遥测缓存...")
        cache_clear_success = self.clear_telemetry_cache()

        total_id_success = (1 if telemetry_reset_success else 0) + (1 if system_guid_reset_success else 0) + (1 if wmi_reset_success else 0) + (1 if cache_clear_success else 0)
        self.log_status(f"[成功] ID重置完成: {total_id_success}/4 个成功")

        # 步骤3: 删除Cursor数据
        self.log_status("[处理] 步骤3: 删除Cursor数据...")

        # 检查目录是否存在
        if not os.path.exists(self.cursor_appdata_path):
            self.log_status("[信息] Cursor配置目录不存在，跳过删除步骤")
            cursor_delete_success = True
        else:
            # 直接调用终极删除方法
            cursor_delete_success = self.suggest_manual_deletion(self.cursor_appdata_path)

        # 显示最终结果
        if cursor_delete_success and total_id_success >= 3:
            self.log_status("[完成] 终极删除完全成功！")
            messagebox.showinfo("删除成功",
                              "终极删除完全成功！\n\n"
                              "✅ Mac机器ID 已重置\n"
                              "✅ 设备ID 已重置\n"
                              "✅ SQMID 已重置\n"
                              "✅ 系统 MachineGuid 已重置\n"
                              "✅ 系统硬件标识符已重置\n"
                              "✅ WMI硬件指纹已重置\n"
                              "✅ 遥测缓存已清除\n"
                              "✅ Cursor数据已删除\n\n"
                              "请重启计算机后点击'刷新机器码'查看新的ID值。")
        elif cursor_delete_success and total_id_success > 0:
            self.log_status("[警告] 终极删除部分成功")
            messagebox.showwarning("部分成功",
                                 f"终极删除部分成功：\n\n"
                                 f"✅ Cursor数据已删除\n"
                                 f"⚠️ ID重置: {total_id_success}/4 个成功\n\n"
                                 "请重启计算机后点击'刷新机器码'查看ID状态。")
        elif cursor_delete_success:
            self.log_status("[警告] 仅Cursor删除成功")
            messagebox.showwarning("部分成功",
                                 "仅Cursor数据删除成功，\n"
                                 "ID重置失败。\n\n"
                                 "请查看日志了解详细信息。")
        elif total_id_success > 0:
            self.log_status("[警告] 仅ID重置成功")
            messagebox.showwarning("部分成功",
                                 f"仅ID重置成功 ({total_id_success}/4)，\n"
                                 "Cursor删除失败。\n\n"
                                 "请重启计算机后点击'刷新机器码'查看新的ID值。")
        else:
            self.log_status("[失败] 终极删除完全失败")
            messagebox.showerror("删除失败",
                               "终极删除完全失败。\n"
                               "请查看日志了解详细信息。")

        # 强制刷新显示，让用户看到新的ID
        self.log_status("[刷新] 正在刷新机器码显示...")
        self.refresh_machine_codes()
        self.log_status("[完成] 机器码显示已刷新，请查看新的ID值")

    def sanitize_message_for_tkinter(self, message):
        """清理消息中的Unicode表情符号，使其兼容Tkinter"""
        # 表情符号映射表
        emoji_map = {
            '🔧': '[修复]',
            '🔄': '[处理]',
            '✅': '[成功]',
            '❌': '[失败]',
            '⚠️': '[警告]',
            '📝': '[记录]',
            '🚀': '[启动]',
            '💡': '[提示]',
            '🔍': '[搜索]',
            '📁': '[文件夹]',
            '📄': '[文件]',
            '🎯': '[目标]',
            '⭐': '[重要]',
            '🎉': '[完成]',
            '🛠️': '[工具]',
            '🔒': '[锁定]',
            '🔓': '[解锁]',
            '💻': '[系统]',
            '🌟': '[特殊]',
            '🎊': '[庆祝]'
        }

        # 替换表情符号
        for emoji, replacement in emoji_map.items():
            message = message.replace(emoji, replacement)

        # 直接返回消息，不再进行Latin-1编码检查
        # 因为我们已经将所有表情符号替换为中文标签
        return message

    def log_status(self, message):
        """记录状态信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        # 清理消息中的表情符号
        safe_message = self.sanitize_message_for_tkinter(message)
        formatted_message = f"[{timestamp}] {safe_message}"

        self.status_text.config(state="normal")
        self.status_text.insert(tk.END, f"\n{formatted_message}")
        self.status_text.see(tk.END)
        self.status_text.config(state="disabled")
        self.root.update()

        # 同时输出到控制台（用于调试，保留原始表情符号）
        original_formatted = f"[{timestamp}] {message}"
        print(original_formatted)
    
    def is_cursor_running(self):
        """检查Cursor是否正在运行"""
        try:
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq Cursor.exe'], 
                                  capture_output=True, text=True, shell=True)
            return 'Cursor.exe' in result.stdout
        except:
            return False
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

def ensure_admin_privileges():
    """确保程序以管理员身份运行"""
    try:
        # 检查是否已经是管理员
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if not is_admin:
            print("========================================")
            print("    Cursor机器码管理器")
            print("========================================")
            print("[警告] 程序需要管理员权限才能正常工作")
            print("[处理] 正在请求管理员权限提升...")
            print("[记录] 请在UAC对话框中点击'是'")
            print("========================================")

            # 以管理员身份重新启动程序
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            # 退出当前进程
            sys.exit(0)
        else:
            print("========================================")
            print("    Cursor机器码管理器 - 管理员模式")
            print("========================================")
            print("[成功] 程序已以管理员身份运行")
            print("[重要] 具有完整的系统权限")
            print("[启动] 正在启动图形界面...")
            print("========================================")
    except Exception as e:
        print("========================================")
        print("    权限获取失败")
        print("========================================")
        print(f"[失败] 获取管理员权限失败: {str(e)}")
        print("")
        print("[提示] 解决方案:")
        print("1. 右键点击程序文件")
        print("2. 选择'以管理员身份运行'")
        print("3. 或者将程序放在非系统保护的目录中")
        print("========================================")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    # 强制要求管理员权限
    ensure_admin_privileges()

    # 启动程序
    app = CursorMachineCodeManager()
    app.run()



